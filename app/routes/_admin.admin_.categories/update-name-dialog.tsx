import { Dialog, DialogContent, DialogDescription, DialogFooter, Di<PERSON>Header, DialogTitle } from '~/components/ui/dialog'
import { useAppForm } from '~/hooks/form'
import useUpdateCategory from './use-update-category'

interface Props {
  open: boolean
  name: string
  toggle: (open?: boolean) => void
  id: string
}

export default function UpdateNameDialog({ open, toggle, name, id }: Props) {
  const { updateName } = useUpdateCategory()

  const form = useAppForm({
    defaultValues: {
      name: name || '',
    },
    onSubmit: async ({ value }) => {
      updateName.mutateAsync({
        id,
        name: value.name,
      }, {
        onSuccess() {
          toggle(false)
        },
      })
    },
  })

  return (
    <Dialog open={open} onOpenChange={toggle}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Update name
          </DialogTitle>
          <DialogDescription>
            Enter new name
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={(e) => {
          e.preventDefault()
          e.stopPropagation()
          form.handleSubmit()
        }}
        >
          <form.AppField
            name="name"
            children={field => <field.InputField label="Name" />}
          />
          <DialogFooter>
            <form.AppForm>
              <form.SubmitButton label="Update" />
            </form.AppForm>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
