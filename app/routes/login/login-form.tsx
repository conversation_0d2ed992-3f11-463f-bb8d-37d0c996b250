import { withForm } from '~/hooks/form'

export const LoginForm = withForm({
  defaultValues: {
    name: '',
    password: '',
  },
  render: ({ form }) => {
    return (
      <div className="mt-4 flex w-full max-w-72 min-w-72 flex-col gap-y-6">
        <form.AppField
          name="name"
          children={field => <field.InputField label="Name" />}
        />
        <form.AppField
          name="password"
          children={field => <field.InputField label="Password" type="password" />}
        />
        <form.AppForm>
          <form.SubmitButton label="Login" />
        </form.AppForm>
      </div>
    )
  },
})
