import { graphql } from '~/gql'

export const ADD_RECORD = graphql(`
  mutation AddRecord(
    $inneih_record: InneihRecordInput
    $baptisma_record: BaptismaRecordInput
    $document: DocumentInput!
  ) {
    addRecord(
      inneih_record: $inneih_record
      baptisma_record: $baptisma_record
      document: $document
    ) 
  }
`)

export const GET_CATEGORIES = graphql(`
  query GetCategories(
    $keyword: String
    $only_leaf: Boolean
    $unnested: Boolean
  ) {
    getCategories(
      keyword: $keyword
      only_leaf: $only_leaf
      unnested: $unnested
    ) {
      id
      name
      parent {
        id
        name
        parent {
          id
          name
        }
      }
    }
  }
`)
