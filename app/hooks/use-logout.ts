import { useMutation } from '@tanstack/react-query'
import { LOGOUT } from '~/graphql/mutation/logout'
import { graphqlClient } from '~/lib/graphql-client'

export default function useLogout() {
  const logout = useMutation({
    mutationFn: async () => {
      const client = await graphqlClient()
      return await client.request({
        document: LOGOUT,
      })
    },
  })

  return { logout }
}
