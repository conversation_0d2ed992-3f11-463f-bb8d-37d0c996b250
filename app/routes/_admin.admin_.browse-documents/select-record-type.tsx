import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'

interface Props {
  value?: string
  onValueChange: (value: string) => void
}

export default function SelectRecordType({ value, onValueChange }: Props) {
  return (
    <Select
      value={value}
      onValueChange={(e) => {
        onValueChange(e)
      }}
    >
      <SelectTrigger className="w-[300px]">
        <SelectValue placeholder="Select record type" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="baptisma">Baptisma record</SelectItem>
        <SelectItem value="inneih">Inneih record</SelectItem>
        <SelectItem value="others">Others</SelectItem>
      </SelectContent>
    </Select>
  )
}
