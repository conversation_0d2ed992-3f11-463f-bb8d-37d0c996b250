import { useQuery } from '@tanstack/react-query'
import { parseAsString, useQueryState } from 'nuqs'
import { useDebounce } from 'use-debounce'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_CATEGORIES_LIST } from './graphql'

export default function useGetCategories() {
  const [search, setSearch] = useQueryState('search', parseAsString.withDefault(''))

  const handleSearch = (e: string) => {
    setSearch(e)
  }

  const [keyword] = useDebounce(search, 500)

  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-categories', search],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_CATEGORIES_LIST,
        variables: {
          keyword,
        },
      })
    },
  })

  return { data, isLoading, isError, handleSearch }
}
