import type { AddSpotlightType } from './schema'
import { useMutation } from '@tanstack/react-query'
import { graphqlClient } from '~/lib/graphql-client'
import { ADD_SPOTLIGHT } from './graphql'

export default function useAddSpotlight() {
  const addSpotlight = useMutation({
    mutationFn: async (data: AddSpotlightType) => {
      const client = await graphqlClient()
      return client.request({
        document: ADD_SPOTLIGHT,
        variables: {
          ...data,
        },
      })
    },
  })

  return { addSpotlight }
}
