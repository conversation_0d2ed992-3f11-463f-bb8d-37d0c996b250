import type { AddRecordType } from './schema'
import { useMutation } from '@tanstack/react-query'
import { format } from 'date-fns'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { ADD_RECORD } from './graphql'

export default function useAddRecord() {
  const addRecord = useMutation({
    mutationFn: async ({ data, categoryName }: { data: AddRecordType, categoryName: string }) => {
      const client = await graphqlClient()
      if (categoryName === 'Inneih Record') {
        return client.request({
          document: ADD_RECORD,
          variables: {
            inneih_record: {
              ...data.inneih_record,
              inneih_ni: data.inneih_record?.inneih_ni ? format(new Date(data.inneih_record.inneih_ni), 'yyyy-MM-dd') : undefined,
            },
            document: {
              ...data.document,
              added_date: data.document.added_date ? format(new Date(data.document.added_date), 'yyyy-MM-dd') : undefined,
            },
          },
        })
      }
      else if (categoryName === 'Baptisma Record') {
        return client.request({
          document: ADD_RECORD,
          variables: {
            baptisma_record: {
              ...data.baptisma_record,
              pian_ni: data.baptisma_record?.pian_ni ? format(new Date(data.baptisma_record.pian_ni), 'yyyy-MM-dd') : undefined,
              baptisma_chan_ni: data.baptisma_record?.baptisma_chan_ni ? format(new Date(data.baptisma_record.baptisma_chan_ni), 'yyyy-MM-dd') : undefined,
            },
            document: {
              ...data.document,
              added_date: data.document.added_date ? format(new Date(data.document.added_date), 'yyyy-MM-dd') : undefined,
            },
          },
        })
      }
      else {
        return client.request({
          document: ADD_RECORD,
          variables: {
            document: {
              ...data.document,
              added_date: data.document.added_date ? format(new Date(data.document.added_date), 'yyyy-MM-dd') : undefined,
            },
          },
        })
      }
    },
    onSuccess: () => {
      toast.success('Document added successfully')
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { addRecord }
}
