import type { LoginType } from './schema'
import { useMutation } from '@tanstack/react-query'
import { graphqlClient } from '~/lib/graphql-client'
import { LOGIN } from './graphql'

export default function useLogin() {
  const login = useMutation({
    mutationFn: async (data: LoginType) => {
      const client = await graphqlClient()
      return await client.request({
        document: LOGIN,
        variables: {
          ...data,
        },
      })
    },
  })

  return { login }
}
