import PageHeader from '~/components/common/page-header'
import AddCategoryDialog from './add-category-dialog'
import CategoryList from './category-list'
import useGetCategories from './use-get-categories'

export default function Categories() {
  const { data } = useGetCategories()

  return (
    <div className="flex grow flex-col gap-4">
      <div className="flex w-full max-w-2xl justify-between pr-2">
        <PageHeader title="Categories" />
        <AddCategoryDialog categories={data} />
      </div>
      <CategoryList categories={data} />
    </div>
  )
}
