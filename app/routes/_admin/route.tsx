import { Outlet } from 'react-router'
import { SidebarProvider, SidebarTrigger } from '~/components/ui/sidebar'
import AdminSidebar from './admin-sidebar'

export function meta() {
  return [
    {
      title: 'Synod Archives - Admin',
    },
  ]
}

export default function Admin() {
  return (
    <div className={`
      mx-auto flex size-full min-h-screen w-full flex-col overflow-hidden
    `}
    >
      <SidebarProvider>
        <div className="flex grow overflow-auto">
          <AdminSidebar />
          <main className="flex grow flex-col p-2">
            <SidebarTrigger />
            <div className="mx-auto mt-4 flex w-full grow flex-col">
              <Outlet />
            </div>
          </main>
        </div>
      </SidebarProvider>
    </div>
  )
}
