import type { GetCategoriesListQuery } from '~/gql/graphql'
import { useState } from 'react'
import AppTooltip from '~/components/common/app-tooltip'
import UpdateIcon from '~/components/icons/update-icon'
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '~/components/ui/accordion'
import { Button } from '~/components/ui/button'
import useBoolean from '~/hooks/use-boolean'
import UpdateButtons from './update-buttons'
import UpdateNameDialog from './update-name-dialog'

interface Props {
  categories?: GetCategoriesListQuery
}

export default function CategoryList({ categories }: Props) {
  const [selectedCategory, setSelectedCategory] = useState({
    id: '',
    name: '',
  })
  const { isOpen, toggle } = useBoolean()
  const { LockCategory, UnlockCategory } = UpdateButtons()

  return (
    <>
      <div className="w-full max-w-2xl text-sm">
        {categories?.getCategories?.map(category => (
          category.children && category.children.length > 0
            ? (
                <Accordion
                  key={category.id}
                  type="multiple"
                  className="w-full"
                >
                  <AccordionItem value={category.id}>
                    <AccordionTrigger className={`
                      flex items-baseline
                      hover:bg-input hover:no-underline
                    `}
                    >
                      <div className="flex w-full items-center justify-between">
                        <div className="text-left">
                          {category.name}
                        </div>
                        <div className="mr-2 flex gap-x-1 rounded-md bg-white" onClick={e => e.stopPropagation()}>
                          <div>
                            <AppTooltip message="Update name">
                              <Button
                                onClick={() => {
                                  toggle(true)
                                  setSelectedCategory({
                                    id: category.id,
                                    name: category.name,
                                  })
                                }}
                                size="icon"
                                variant="ghost"
                              >
                                <UpdateIcon />
                              </Button>

                            </AppTooltip>
                          </div>
                          <div>
                            {category.is_classified
                              ? (
                                  <UnlockCategory id={category.id} />
                                )
                              : (
                                  <LockCategory id={category.id} />
                                )}
                          </div>
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="pl-12">
                        {category.children.map(child => (
                          child.children && child.children.length > 0
                            ? (
                                <Accordion
                                  key={child.id}
                                  type="multiple"
                                  className="w-full"
                                >
                                  <AccordionItem value={child.id}>
                                    <AccordionTrigger className={`
                                      flex items-baseline
                                      hover:bg-input hover:no-underline
                                    `}
                                    >
                                      <div className={`
                                        flex w-full items-center justify-between
                                      `}
                                      >
                                        <div className="text-left">
                                          {child.name}
                                        </div>
                                        <div
                                          className={`
                                            mr-2 flex gap-x-1 rounded-md
                                            bg-white
                                          `}
                                          onClick={e => e.stopPropagation()}
                                        >
                                          <div>
                                            <AppTooltip message="Update name">
                                              <Button
                                                onClick={() => {
                                                  toggle(true)
                                                  setSelectedCategory({
                                                    id: child.id,
                                                    name: child.name,
                                                  })
                                                }}
                                                size="icon"
                                                variant="ghost"
                                              >
                                                <UpdateIcon />
                                              </Button>
                                            </AppTooltip>
                                          </div>
                                          <div>
                                            {child.is_classified
                                              ? (
                                                  <UnlockCategory id={child.id} />
                                                )
                                              : (
                                                  <LockCategory id={child.id} />
                                                )}
                                          </div>
                                        </div>
                                      </div>
                                    </AccordionTrigger>
                                    <AccordionContent>
                                      <div className="pl-16">
                                        {child.children.map(grandchild => (
                                          <div
                                            key={grandchild.id}
                                            className={`
                                              flex flex-1 items-center
                                              justify-between gap-4 rounded-md
                                              py-4
                                              hover:bg-input
                                            `}
                                          >
                                            <div className="pl-1 text-left">
                                              {grandchild.name}
                                            </div>
                                            <div className={`
                                              mr-2 flex gap-x-1 rounded-md
                                              bg-white
                                            `}
                                            >
                                              <div>
                                                <AppTooltip message="Update name">
                                                  <Button
                                                    onClick={() => {
                                                      toggle(true)
                                                      setSelectedCategory({
                                                        id: grandchild.id,
                                                        name: grandchild.name,
                                                      })
                                                    }}
                                                    size="icon"
                                                    variant="ghost"
                                                  >
                                                    <UpdateIcon />
                                                  </Button>
                                                </AppTooltip>
                                              </div>
                                              <div>
                                                {grandchild.is_classified
                                                  ? (
                                                      <UnlockCategory id={grandchild.id} />
                                                    )
                                                  : (
                                                      <LockCategory id={grandchild.id} />
                                                    )}
                                              </div>
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    </AccordionContent>
                                  </AccordionItem>
                                </Accordion>
                              )
                            : (
                                <div
                                  key={child.id}
                                  className={`
                                    flex items-center justify-between rounded-md
                                    py-4 pl-8
                                    hover:bg-input
                                  `}
                                >
                                  <div>
                                    {child.name}
                                  </div>
                                  <div className={`
                                    mr-2 flex gap-x-1 rounded-md bg-white
                                  `}
                                  >
                                    <div>
                                      <AppTooltip message="Update name">
                                        <Button
                                          onClick={() => {
                                            toggle(true)
                                            setSelectedCategory({
                                              id: child.id,
                                              name: child.name,
                                            })
                                          }}
                                          size="icon"
                                          variant="ghost"
                                        >
                                          <UpdateIcon />
                                        </Button>
                                      </AppTooltip>
                                    </div>
                                    <div>
                                      {child.is_classified
                                        ? (
                                            <UnlockCategory id={child.id} />
                                          )
                                        : (
                                            <LockCategory id={child.id} />
                                          )}
                                    </div>
                                  </div>
                                </div>
                              )
                        ))}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              )
            : (
                <div
                  key={category.id}
                  className={`
                    flex items-center justify-between rounded-md py-4 pl-8
                    hover:bg-input
                  `}
                >
                  <div>
                    {category.name}
                  </div>
                  <div className="mr-2 flex gap-x-1 rounded-md bg-white">
                    <div>
                      <AppTooltip message="Update name">
                        <Button
                          onClick={() => {
                            toggle(true)
                            setSelectedCategory({
                              id: category.id,
                              name: category.name,
                            })
                          }}
                          size="icon"
                          variant="ghost"
                        >
                          <UpdateIcon />
                        </Button>
                      </AppTooltip>
                    </div>
                    <div>
                      {category.is_classified
                        ? (
                            <UnlockCategory id={category.id} />
                          )
                        : (
                            <LockCategory id={category.id} />
                          )}
                    </div>
                  </div>
                </div>
              )
        ))}
      </div>
      {selectedCategory.id && (
        <UpdateNameDialog
          id={selectedCategory.id}
          name={selectedCategory.name}
          open={isOpen}
          toggle={toggle}
        />
      )}
    </>
  )
}
