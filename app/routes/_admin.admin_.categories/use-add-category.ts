import type { AddCategoryMutationVariables } from '~/gql/graphql'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { ADD_CATEGORY } from './graphql'

export default function useAddCategory() {
  const queryClient = useQueryClient()

  const addCategory = useMutation({
    mutationFn: async ({ name, parent_id, is_classified }: AddCategoryMutationVariables) => {
      const client = await graphqlClient()
      return client.request({
        document: ADD_CATEGORY,
        variables: {
          name,
          parent_id,
          is_classified,
        },
      })
    },
    onSuccess: () => {
      toast.success('Category added')
      queryClient.invalidateQueries({
        queryKey: ['get-categories'],
      })
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { addCategory }
}
