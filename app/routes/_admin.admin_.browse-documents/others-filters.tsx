import { format } from 'date-fns'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import { useAppForm } from '~/hooks/form'
import useGetDocuments from './use-get-documents'

export default function OthersFilters() {
  const { getOthers, handlePage } = useGetDocuments()
  const form = useAppForm({
    defaultValues: {
      title: '',
      body: '',
      tag: '',
      added_date: '',
    },
    onSubmit: async ({ value }) => {
      getOthers.mutateAsync(value, {
        onSuccess: () => console.warn('data'),

      })
    },
  })

  const data = getOthers.data?.getDocuments?.data || []

  return (
    <>
      <Card>
        <CardContent>
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              handlePage(1)
              form.handleSubmit()
            }}
            className="grid grid-cols-4 gap-4"
          >
            <form.AppField
              name="title"
              children={field => <field.InputField label="Title" />}
            />
            <form.AppField
              name="body"
              children={field => <field.InputField label="Body" />}
            />
            <form.AppField
              name="tag"
              children={field => <field.InputField label="tag" />}
            />
            <form.AppField
              name="added_date"
              children={field => <field.InputField label="Added date" type="date" />}
            />

            <div className="col-span-1">
              <Button type="submit" isLoading={getOthers.isPending}>
                Search
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
      {data?.length > 0 && (
        <Table className="w-full min-w-[1000px] table-fixed rounded-md">
          <TableHeader>
            <TableRow>
              <TableHead className="w-32">Title</TableHead>
              <TableHead className="w-64">Body</TableHead>
              <TableHead className="w-48">Tags</TableHead>
              <TableHead className="w-24">Attached files</TableHead>
              <TableHead className="w-32">Added on</TableHead>
              <TableHead className="w-32">Classified</TableHead>
              <TableHead className="w-32 text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data?.map(item => (
              item && (
                <TableRow key={item.id}>
                  <TableCell className="align-top">{item.title || '-'}</TableCell>
                  <TableCell className="align-top">
                    {item.body
                      ? (
                          <div
                            dangerouslySetInnerHTML={{ __html: item.body }}
                            className="whitespace-pre-line"
                          />
                        )
                      : '-'}
                  </TableCell>
                  <TableCell className="align-top">{item.tags || '-'}</TableCell>
                  <TableCell className="align-top">{item.files?.length || '0'}</TableCell>
                  <TableCell className="align-top">{item.added_date ? format(new Date(item.added_date), 'yyyy-MM-dd') : '-' }</TableCell>
                  <TableCell className="align-top">{item.is_classified ? 'Yes' : 'No'}</TableCell>
                  <TableCell className="text-right align-top">-</TableCell>
                </TableRow>
              )))}
          </TableBody>
        </Table>
      )}
    </>
  )
}
