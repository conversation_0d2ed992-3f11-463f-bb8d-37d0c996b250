import AppTooltip from '~/components/common/app-tooltip'
import LockIcon from '~/components/icons/lock-icon'
import UnlockIcon from '~/components/icons/unlock-icon'
import { But<PERSON> } from '~/components/ui/button'
import useUpdateCategory from './use-update-category'

export default function UpdateButtons() {
  const { updateClassified } = useUpdateCategory()

  const LockCategory = ({ id }: { id: string }) => {
    return (
      <AppTooltip message="Unlock">
        <Button
          onClick={() => {
            updateClassified.mutate({
              id,
              is_classified: true,
            })
          }}
          size="icon"
          variant="destructive"
        >
          <LockIcon />
        </Button>
      </AppTooltip>
    )
  }

  const UnlockCategory = ({ id }: { id: string }) => {
    return (
      <AppTooltip message="Lock">
        <Button
          onClick={() => {
            updateClassified.mutate({
              id,
              is_classified: false,
            })
          }}
          size="icon"
          variant="success"
        >
          <UnlockIcon />
        </Button>
      </AppTooltip>
    )
  }

  return { LockCategory, UnlockCategory }
}
