import { useState } from 'react'
import PageHeader from '~/components/common/page-header'
import BaptismaFilters from './baptisma-filters'
import InneihFilters from './inneih-filters'
import OthersFilters from './others-filters'
import SelectRecordType from './select-record-type'

export default function BrowseDocuments() {
  const [selectedRecordType, setSelectedRecordType] = useState('')

  const handleRecordType = (value: string) => {
    setSelectedRecordType(value)
  }

  return (
    <div className="flex grow flex-col gap-4">
      <PageHeader title="Browse Documents" />
      <SelectRecordType value={selectedRecordType} onValueChange={handleRecordType} />
      {selectedRecordType === 'baptisma' && (
        <BaptismaFilters />
      )}
      {selectedRecordType === 'inneih' && (
        <InneihFilters />
      )}
      {selectedRecordType === 'others' && (
        <OthersFilters />
      )}
    </div>
  )
}
