import type { BaptismaRecordFilterInput, InneihRecordFilterInput, OtherRecordInput } from '~/gql/graphql'
import { useMutation } from '@tanstack/react-query'
import { format } from 'date-fns'
import { parseAsInteger, useQueryState } from 'nuqs'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_DOCUMENTS } from './graphql'

export default function useGetDocuments() {
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1))

  const handlePage = (page: number) => {
    setPage(page)
  }

  const getBaptisma = useMutation({
    mutationKey: ['get-baptisma', page],
    mutationFn: async (data: BaptismaRecordFilterInput) => {
      const client = await graphqlClient()
      return client.request({
        document: GET_DOCUMENTS,
        variables: {
          first: 20,
          page,
          baptisma_filter: {
            ...data,
            pian_ni: data.pian_ni ? format(new Date(data.pian_ni), 'yyyy-MM-dd') : undefined,
            baptisma_chan_ni: data.baptisma_chan_ni ? format(new Date(data.baptisma_chan_ni), 'yyyy-MM-dd') : undefined,
          },
        },
      })
    },
  })

  const getInneih = useMutation({
    mutationKey: ['get-inneih', page],
    mutationFn: async (data: InneihRecordFilterInput) => {
      const client = await graphqlClient()
      return client.request({
        document: GET_DOCUMENTS,
        variables: {
          first: 20,
          page,
          inneih_filter: {
            ...data,
            inneih_ni: data.inneih_ni ? format(new Date(data.inneih_ni), 'yyyy-MM-dd') : undefined,
          },
        },
      })
    },
  })

  const getOthers = useMutation({
    mutationKey: ['get-others', page],
    mutationFn: async (data: OtherRecordInput) => {
      const client = await graphqlClient()
      return client.request({
        document: GET_DOCUMENTS,
        variables: {
          first: 20,
          page,
          others_filter: {
            ...data,
            added_date: data.added_date ? format(new Date(data.added_date), 'yyyy-MM-dd') : undefined,
          },
        },
      })
    },
  })

  return { getBaptisma, page, handlePage, getInneih, getOthers }
}

// export default function useGetDocuments() {
//   const [filter, setFilter] = useQueryStates({
//     // Pagination parameters
//     first: parseAsInteger.withDefault(10),
//     page: parseAsInteger.withDefault(1),

//     // Common filters
//     added_date: parseAsString,
//     // Filter type selector - determines which specific filter is active
//     filter_type: parseAsStringEnum(['baptisma', 'inneih', 'others'] as const),

//     // Baptisma record filters
//     baptisma_filter: parseAsJson<Partial<BaptismaRecordFilterInput>>(val => val as Partial<BaptismaRecordFilterInput>),

//     // Inneih record filters
//     inneih_filter: parseAsJson<Partial<InneihRecordFilterInput>>(val => val as Partial<InneihRecordFilterInput>),

//     // Other record filters
//     others_filter: parseAsJson<Partial<OtherRecordInput>>(val => val as Partial<OtherRecordInput>),
//   })

//   const handleBaptismaFilter = (baptismaFilter: Partial<BaptismaRecordFilterInput>) => {
//     setFilter({
//       filter_type: 'baptisma',
//       baptisma_filter: baptismaFilter,
//       inneih_filter: null,
//       others_filter: null,
//     })
//   }

//   const handleFilterType = (type: string) => {
//     setFilter({
//       filter_type: type as 'baptisma' || 'inneih' || 'others',
//       baptisma_filter: null,
//       inneih_filter: null,
//       others_filter: null,
//     })
//   }

//   const handleInneihFilter = (inneihFilter: Partial<InneihRecordFilterInput>) => {
//     setFilter({
//       filter_type: 'inneih',
//       inneih_filter: inneihFilter,
//       baptisma_filter: null,
//       others_filter: null,
//     })
//   }

//   const handleOthersFilter = (othersFilter: Partial<OtherRecordInput>) => {
//     setFilter({
//       filter_type: 'others',
//       others_filter: othersFilter,
//       baptisma_filter: null,
//       inneih_filter: null,
//     })
//   }

//   const clearFilters = () => {
//     setFilter({
//       filter_type: null,
//       baptisma_filter: null,
//       inneih_filter: null,
//       others_filter: null,
//     })
//   }

//   const { data, isLoading, isError } = useQuery({
//     queryKey: ['documents', filter],
//     queryFn: async () => {
//       const client = await graphqlClient()

//       // Prepare variables based on active filter type
//       const variables = {
//         first: filter.first,
//         page: filter.page,
//         added_date: filter.added_date || undefined,
//         baptisma_filter: null,
//         inneih_filter: null,
//         others_filter: null,
//       }

//       // Only include the active filter type
//       if (filter.filter_type === 'baptisma' && filter.baptisma_filter) {
//         variables.baptisma_filter = filter.baptisma_filter
//         // variables.baptisma_filter.
//       }
//       else if (filter.filter_type === 'inneih' && filter.inneih_filter) {
//         variables.inneih_filter = filter.inneih_filter
//       }
//       else if (filter.filter_type === 'others' && filter.others_filter) {
//         variables.others_filter = filter.others_filter
//       }

//       return client.request({
//         document: GET_DOCUMENTS,
//         variables: {
//           first: filter.first,
//           page: filter.page,
//           inneih_filter: filter?.inneih_filter ? filter.inneih_filter : undefined,
//           baptisma_filter: filter?.baptisma_filter ? filter.baptisma_filter : undefined,
//           others_filter: filter?.others_filter ? filter.others_filter : undefined,
//         },
//       })
//     },
//     enabled: !!filter.baptisma_filter || !!filter.inneih_filter || !!filter.others_filter,
//   })

//   return {
//     data,
//     isLoading,
//     isError,
//     filter,
//     setFilter,
//     handleBaptismaFilter,
//     handleInneihFilter,
//     handleOthersFilter,
//     clearFilters,
//     handleFilterType,
//   }
// }
